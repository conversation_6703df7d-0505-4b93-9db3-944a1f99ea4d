<?php $__env->startSection('title', 'จัดการบริการ - SoloShop Admin'); ?>

<?php $__env->startSection('content'); ?>
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-cogs me-2"></i>จัดการบริการ
                </h1>
                <p class="text-muted mb-0">จัดการบริการทั้งหมดของคุณ</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="toggleView()">
                    <i class="fas fa-th-large me-2" id="viewIcon"></i>
                    <span id="viewText">มุมมองการ์ด</span>
                </button>
                <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">บริการทั้งหมด</h6>
                        <h3 class="mb-0"><?php echo e($services->count()); ?></h3>
                    </div>
                    <i class="fas fa-cogs fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">บริการที่เปิดใช้</h6>
                        <h3 class="mb-0"><?php echo e($services->where('is_active', 1)->count()); ?></h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">บริการที่ปิดใช้</h6>
                        <h3 class="mb-0"><?php echo e($services->where('is_active', 0)->count()); ?></h3>
                    </div>
                    <i class="fas fa-times-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Services Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>รายการบริการ
            </h5>
            <div class="d-flex gap-2">
                <div class="input-group" style="width: 300px;">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="ค้นหาบริการ..." id="searchInput">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if($services->count() > 0): ?>

        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">รูปภาพ</th>
                            <th>ชื่อบริการ</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 100px;">สถานะ</th>
                            <th style="width: 80px;">ลำดับ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="service-row">
                            <td>
                                <?php if($service->image): ?>
                                <img src="<?php echo e(asset('storage/' . $service->image)); ?>" alt="<?php echo e($service->title); ?>"
                                     class="img-thumbnail rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                <div class="bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center rounded"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <strong class="service-title"><?php echo e($service->title); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo e($service->created_at->format('d/m/Y')); ?>

                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="service-description"><?php echo e(Str::limit($service->description, 80)); ?></span>
                            </td>
                            <td>
                                <?php if($service->is_active): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>เปิดใช้
                                </span>
                                <?php else: ?>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>ปิดใช้
                                </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($service->sort_order ?? 0); ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.services.edit', $service->id)); ?>"
                                       class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.services.delete', $service->id)); ?>"
                                          method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Card View -->
        <div id="cardView" style="display: none;">
            <div class="row g-4">
                <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-lg-4 service-card">
                    <div class="card h-100 interactive-card">
                        <?php if($service->image): ?>
                        <img src="<?php echo e(asset('storage/' . $service->image)); ?>" class="card-img-top"
                             style="height: 200px; object-fit: cover;" alt="<?php echo e($service->title); ?>">
                        <?php else: ?>
                        <div class="card-img-top bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center"
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <h5 class="card-title service-title"><?php echo e($service->title); ?></h5>
                            <p class="card-text service-description"><?php echo e(Str::limit($service->description, 100)); ?></p>

                            <div class="d-flex justify-content-end align-items-center mb-3">
                                <?php if($service->is_active): ?>
                                <span class="badge bg-success">เปิดใช้</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">ปิดใช้</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('admin.services.edit', $service->id)); ?>"
                                   class="btn btn-primary flex-fill">
                                    <i class="fas fa-edit me-2"></i>แก้ไข
                                </a>
                                <form action="<?php echo e(route('admin.services.delete', $service->id)); ?>"
                                      method="POST" class="flex-fill">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger w-100"
                                            onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                                        <i class="fas fa-trash me-2"></i>ลบ
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Pagination -->
        <?php if($services->hasPages()): ?>
        <div class="mt-4 p-3 bg-light rounded">
            <?php echo $__env->make('custom.admin-pagination', ['paginator' => $services], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-cogs fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีบริการ</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มบริการแรกของคุณ</p>
            <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
let currentView = 'table';

function toggleView() {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    if (currentView === 'table') {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
        viewIcon.className = 'fas fa-list me-2';
        viewText.textContent = 'มุมมองตาราง';
        currentView = 'card';
    } else {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
        viewIcon.className = 'fas fa-th-large me-2';
        viewText.textContent = 'มุมมองการ์ด';
        currentView = 'table';
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const serviceRows = document.querySelectorAll('.service-row');
    const serviceCards = document.querySelectorAll('.service-card');

    // Search in table view
    serviceRows.forEach(row => {
        const title = row.querySelector('.service-title').textContent.toLowerCase();
        const description = row.querySelector('.service-description').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Search in card view
    serviceCards.forEach(card => {
        const title = card.querySelector('.service-title').textContent.toLowerCase();
        const description = card.querySelector('.service-description').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
});

// Add animation to cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/services/index.blade.php ENDPATH**/ ?>