<?php if($paginator->hasPages()): ?>
<nav class="d-flex justify-content-between align-items-center flex-wrap gap-3">
    <div class="pagination-info">
        <i class="fas fa-info-circle me-1"></i>
        แสดง <strong><?php echo e($paginator->firstItem()); ?></strong> ถึง <strong><?php echo e($paginator->lastItem()); ?></strong>
        จากทั้งหมด <strong><?php echo e($paginator->total()); ?></strong> รายการ
    </div>

    <ul class="pagination mb-0">
        
        <?php if($paginator->onFirstPage()): ?>
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </li>
        <?php else: ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        <?php endif; ?>

        
        <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            
            <?php if(is_string($element)): ?>
                <li class="page-item disabled"><span class="page-link"><?php echo e($element); ?></span></li>
            <?php endif; ?>

            
            <?php if(is_array($element)): ?>
                <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($page == $paginator->currentPage()): ?>
                        <li class="page-item active">
                            <span class="page-link"><?php echo e($page); ?></span>
                        </li>
                    <?php else: ?>
                        <li class="page-item">
                            <a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        
        <?php if($paginator->hasMorePages()): ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        <?php else: ?>
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </li>
        <?php endif; ?>
    </ul>

    <div class="pagination-jump d-flex align-items-center">
        <span class="text-muted small me-2">
            <i class="fas fa-external-link-alt me-1"></i>ไปหน้า:
        </span>
        <select class="form-select form-select-sm" onchange="goToPage(this.value)">
            <?php for($i = 1; $i <= $paginator->lastPage(); $i++): ?>
                <option value="<?php echo e($paginator->url($i)); ?>" <?php echo e($i == $paginator->currentPage() ? 'selected' : ''); ?>>
                    <?php echo e($i); ?>

                </option>
            <?php endfor; ?>
        </select>
    </div>
</nav>



<script>
function goToPage(url) {
    if (url) {
        // Add loading state
        const select = event.target;
        const originalText = select.options[select.selectedIndex].text;
        select.disabled = true;

        // Show loading overlay
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay show';
        overlay.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">กำลังโหลดหน้า ${originalText}...</p>
            </div>
        `;
        document.body.appendChild(overlay);

        // Navigate to page
        window.location.href = url;
    }
}

// Add smooth transitions to pagination links
document.addEventListener('DOMContentLoaded', function() {
    const paginationLinks = document.querySelectorAll('.pagination .page-link');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!this.closest('.page-item').classList.contains('disabled') &&
                !this.closest('.page-item').classList.contains('active')) {

                // Add loading state
                this.style.pointerEvents = 'none';
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                // Show page loading
                setTimeout(() => {
                    const overlay = document.createElement('div');
                    overlay.className = 'loading-overlay show';
                    overlay.innerHTML = `
                        <div class="text-center">
                            <div class="loading-spinner"></div>
                            <p class="mt-3 text-muted">กำลังโหลดหน้าถัดไป...</p>
                        </div>
                    `;
                    document.body.appendChild(overlay);
                }, 100);
            }
        });
    });
});
</script>
<?php endif; ?>
<?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/custom/admin-pagination.blade.php ENDPATH**/ ?>